{"kazawallet": "Ka<PERSON>llet", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "exchange": "Exchange", "exchangeWord": "Exchange", "withdraw": "Withdraw", "give": "Give", "get": "Get", "balance": "Balance", "transfer": "Transfer", "rate": "Rate", "flexiCard": "Flexi Card", "chooseADocument": "Choose A Document", "chooseAImage": "<PERSON>ose A <PERSON>", "maxFileSize": "<PERSON> Si<PERSON>", "email": "Email", "accountNo": "Account No", "accountType": "Account Type", "mobile": "Mobile Phone", "telegram": "Telegram", "darkMode": "Dark", "lightMode": "Light", "massPayout": "Mass Payout", "history": "History", "wallets": "Wallets", "logout": "Logout", "send": "Send", "loginOrRegister": "Register / Login", "myAccount": "My Account", "getStarted": "Get Started", "copy": "Copy", "copied": "<PERSON>pied", "save": "Save", "myBalance": "Balance", "chosePaymentSystem": "Choose Payment Method", "selectPaymentSystem": "Select Payment Method", "all": "All", "recentTransactions": "Recent transactions", "recentDepositTransactions": "Recent deposit transactions", "recentWithdrawTransactions": "Recent withdraw transactions", "recentTransferTransactions": "Recent transfer transactions", "recentExchangeTransactions": "Recent exchange transactions", "recentReceivedTransactions": "Recent receive transactions", "clickHereToGetFileTemplate": "Click Here To Get File Template", "selectCsvFile": "Please upload your CSV file here", "fileSelected": "File uploaded", "confirm": "Confirm", "thisFileIsEmpty": "This file is empty", "enterValidFile": "Enter a valid file", "incorrectFileFormatting": "Incorrect file formatting please check file template", "pleaseEnterCsvFile": "Please enter a CSV file", "amount": "Amount", "accountNumberEmail": "Account Number/ Email", "total": "Total", "note": "Note", "name": "Name", "priceUsd": "Price USD", "priceSyp": "Price SYP", "commission": "Commission", "toBeReceived": "To Be Received", "operationDetails": "Operation Details", "operationNo": "Operation No", "date": "Date", "operationType": "Operation Type", "status": "Status", "sender": "Sender", "comment": "Comment", "pending": "Pending", "approved": " Approved", "rejected": " Rejected", "processing": "Processing", "success": "Success", "failed": "Failed", "min": "Min", "max": "Max", "walletAddress": "Wallet Address", "description": "Description", "completeOperation": "Complete the operation", "close": "Close", "areYouSure": "Are You Sure !", "fees": "Fees", "transferSuccess": "Your transfer has been successful", "totalBalance": "Total Balance", "paymentMethods": "Payment Methods", "exchangeRates": "Currency exchange rates to the US dollar", "lastUpdate": "Last Update", "depositAddress": "Deposit Address", "exchangeSuccess": "Your exchange has been successful", "price": "Price", "extendButtonQuestion": "Are you sure you want to continue extending your number ?", "login": "<PERSON><PERSON>", "loginToYourAccount": "Login to Your Account", "password": "Password", "yourPassword": "Your Password", "keepMeLoggedIn": "Keep me Logged In", "forgotPassword": "Forgot Password", "dontHaveAnAccount": "Don't have an account?", "register": "Register", "confirmationLinkSentToYourEmail": "Confirmation Link Sent To Your Email", "emailConfirmed": "Email Confirmed", "confirmYourEmail": "Confirm Your Email", "youllGetAnEmailWithAConfirmationLink": "You'll get an email with a confirmation link", "backToLogin": "Back To Login", "forgotYourPassword": "Forgot your password?", "sendRequest": "Send Request", "areYouSureYouWantToLogout": "Are you sure you want to logout?", "confirmPassword": "Confirm Password", "alreadyAUser": "Already have an account?", "resetPassword": "Reset Password", "enterNewPassword": "Enter new password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "emailAddress": "Email Address", "resendConfirmationEmailButton": "Resend confirmation email", "minMaxErrorDeposit": "The value must be between the maximum and minimum", "confirmedSuccess": "Your account has been confirmed successfully", "selectCurrencyGet": "Select Currency (Get)", "selectCurrencyGive": "Select Currency (Give)", "depositSuccess": "Your deposit has been successful", "withdrawSuccess": "Your withdraw has been successful", "completed": "Completed", "reset": "Reset", "showMore": "Show More", "currency": "<PERSON><PERSON><PERSON><PERSON>", "changeYourPassword": "Change Your Password", "currentPassword": "Current Password", "changePassword": "Change Password", "changePasswordSuccess": "Change Password Success", "receiver": "Receiver", "currencyTo": "C<PERSON>rency To", "actualAmount": "Actual Amount", "paymentMethod": "Payment Method", "exchangedCurrency": "Exchanged Currency", "receivedAmount": "Received Amount", "noPaymentMethodSupportThisCurrency": "There are no payment methods that support this currency", "noDataToShow": "No Data To Show", "main": "Main", "received": "Receive", "thePrice": "Amount", "sentAmount": "<PERSON><PERSON>", "from": "From", "to": "To", "toBeDeposit": "<PERSON> <PERSON>", "paymentMethodFees": "Payment Method Fees", "usdPrice": "USD ≈", "rates": "Rates", "settings": "Settings", "toBeDepositError": "The deposited amount less than or equal zero you should increase the amount", "moreDetails": "More details", "lastName": "Last Name", "firstName": "First Name", "avatarUpdateSuccess": "Your avatar updating successfully", "pickOne": "Chose country", "country": "Country", "heroLandingSubTitle": "Multi-currency Wallet", "heroLandingTitle": "Your perfect solution for digital financial needs.", "heroLandingDescription": "Your account with Kazawallet offers a wide range of options to meet your digital financial needs. Whether you're making international payments, receiving funds, or managing your assets with ease and security, we've got the ideal solution for you.", "heroLandingButton": "Create your account", "heroLandingComingSoon": "Coming soon on Apple Store.", "infoLandingTitle": "A Faster and Easier Financial Experience with Ka<PERSON><PERSON><PERSON>", "globalCurrencies": "Global Currencies", "fiatCurrencies": "Fiat currencies", "cryptoCurrencies": "Crypto currencies", "cryptocurrencies": "Cryptocurrencies", "paymentSystems": "Payment Systems", "countriesSupported": "Countries Supported", "aboutLandingTitle": "Multiple Options, One Account", "aboutLandingDescription": "We've got you covered with all your financial needs through just one account. Now you can manage all your currencies and transactions with just a few taps on your smartphone. No need to subscribe to multiple accounts or deal with the hassle of managing multiple financial apps.", "securityLandingTitle": "World-class Security for Our Platform", "securityLandingDescriptionFirst": "We take pride in providing a secure and reliable currency management platform on a global scale. We adhere to the highest safety standards to ensure the protection of your assets and personal data.", "securityLandingDescriptionSecond": "Our leading safety measures include advanced encryption technology and highly efficient security teams continuously monitoring the system. We are committed to combating fraud and manipulation, ensuring smooth and reliable financial operations for our customers.", "advantagesLandingTitle": " Amazing Features of", "advantagesLandingFeat1Title": "Unmatched Security", "advantagesLandingFeat1SubTitle": "Safeguard your digital assets with Kazawallet.", "advantagesLandingFeat1Description": "Our platform is designed with the highest security standards, ensuring the safety and protection of your digital assets from any attack or breach. You can rest assured that your wallet is fully secure 24/7.", "advantagesLandingFeat2Title": "Superfast Transactions", "advantagesLandingFeat2SubTitle": "Seize opportunities swiftly with Kazawallet.", "advantagesLandingFeat2Description": "Leveraging modern technology, we ensure that your transactions and exchanges are fast and seamless. No opportunities will be missed in the world of digital assets thanks to our swift transactions.", "advantagesLandingFeat3Title": "Fair Exchange Rates", "advantagesLandingFeat3SubTitle": "Take control of your finances with transparent and reasonable rates.", "advantagesLandingFeat3Description": "Your account comes integrated with foreign currencies, and we update the exchange rates every 10 minutes, allowing you to monitor and control your money with transparent and fair rates.", "advantagesLandingFeat4Title": "24/7 Support", "advantagesLandingFeat4SubTitle": "Continuous support for you and your digital assets.", "advantagesLandingFeat4Description": "At Kazawallet, we care about you and your digital assets. Our professional support team is ready to guide you and resolve any issues you may encounter, anytime, day or night.", "advantagesLandingFeat5Title": "Borderless Trading", "advantagesLandingFeat5SubTitle": "Reach your customers worldwide.", "advantagesLandingFeat5Description": "Now you can access your customers in Europe, the Middle East, Asia, and anywhere else in the world. Your new customers will be able to purchase from you without worrying about payment methods.", "advantagesLandingFeat6Title": "Multiple Payment Methods", "advantagesLandingFeat6SubTitle": "Facilitate receiving digital and electronic currencies from around the world.", "advantagesLandingFeat6Description": "Start receiving major digital currencies and e-wallets from around the world in just a few steps and benefit from the diversity of payment methods to attract more customers.", "start": "Start", "downloadLandingTitle": "Your Journey with Kazawallet Now", "downloadLandingDescriptionFirst": "Do you want to exchange and manage your digital assets easily, quickly, and securely?", "downloadLandingDescriptionSecond": "Start your financial journey with Kazawallet today and enjoy unparalleled advantages!", "downloadLandingDescriptionThird": "Join us now and explore a world of opportunities and enhancements offered by our trusted platform. Benefit from unmatched security for your digital assets and modern technology ensuring fast and smooth transactions.", "kazawalletDesc": "<PERSON><PERSON><PERSON><PERSON> lets you manage all your currencies and wallets in one place. Whether you need to exchange, send, or receive money, <PERSON><PERSON><PERSON><PERSON> makes it easy.", "defaultTitle": "The One Wallet for All Your Needs.", "defaultSeoTitle": "The Ultimate Wallet for All Currencies | Kazawallet", "advantages": "Advantages", "information": "Information", "termsOfUse": "Terms of Use", "privacyNotice": "Privacy Notice", "amlPolicy": "AML Policy", "support": "Support", "helpCenter": "Help Center", "faqs": "FAQs", "contactUs": "Contact Us", "supportEmail": "<EMAIL>", "imageDesc": "All image types (JPG, PNG, WEBP, etc.). Max size: {{value}} MB.", "uploading": "File uploading", "joinUsToday": "Join Us <PERSON>", "cancel": "Cancel", "showAllRates": "Show All Rates", "adminMessage": "Admin Message", "massPayoutFirstStep": "Download the attached file.", "massPayoutSecondStep": "Fill in transfer details for your recipients.", "massPayoutThirdStep": "Upload the file.", "firstStep": "First Step", "secondStep": "Second Step", "thirdStep": "Third Step", "googleAuthenticator": "Google Authenticator", "googleAuthenticatorSettings": "Google Authenticator Settings", "enabled": "Enabled", "disabled": "Disabled", "scanQrCode": "Add your token to your authenticator app by scan the Qr code", "authToken": "Or copy token authenticator", "generateCode": "Generate New Code", "showQrCode": "Show My Code", "enterCode": "Enter the code generated by your authenticator", "enterCodeSent": "Enter the code we sent to your email", "linkExpired": "The link has been expired", "returnTo": "Return to", "youGetInvoiceFrom": "You got invoice from", "doNotHave": "Don't have", "exchangeNow": "Exchange now", "store": "Store", "orderId": "Order ID", "paymentFrom": "Payment from", "pay": "Pay", "shouldGenerateCode": "You should generate QR code and scan it or copy code", "selectCurrency": "Select Currency", "redirectUrl": "Redirect Url", "paymentLink": "Payment Link", "addAuthenticatorSettingsSuccess": "Save your authenticator settings successful", "byPaymentLink": "Made using a payment link from", "changeGif": "Change the Gif", "chooseGif": "Choose a Gif", "fileName": "File name", "pleaseCreateDeposit": " Please create a deposit", "noCurrenciesAvailableToThisOperation": "No Currencies Available For This Operation", "apiKey": "Api Key", "gotoWallets": "Go to wallets", "createPaymentLink": "Create payment link", "youWillBeRedirected": "You will be redirected to another page after", "linkUsedBefore": "This link is already used before", "youDoNotHaveBalanceToDoOperation": "You don't have balance to do this operation", "reGenerateApiKey": "Regenerate Api Key", "search": "Search", "thisLinkActiveUntil": "This Link Active Until", "regenerateApiKeySuccessful": "Regenerate Api Key Successful", "error": "Error", "createAccount": "Create an account", "signIn": "Sign In", "signUp": "Sign Up", "registerTermsFirstPart": "By clicking the “Sign Up” button, you are creating a Kasroad account, and you agree to Ka<PERSON><PERSON>", "registerTermsSecondPart": "Terms of use", "registerTermsThirdPart": " Privacy Policy.", "and": "and", "registerFirstNote": "If you have previously an account on <a href='https://non-voip.com' target='_blank' style='text-decoration:none' > Non-VoIP </a>, then it works with the same email and password on Kazawallet", "registerNote": "By clicking the “Register” button, you are creating a Kasroad account, and you agree to Kasroads Terms of use and Privacy Policy", "refresh": "Refresh", "available": "Available", "loginPlease": "Login Please", "apiUsage": "API Usage", "language": "Language", "english": "English", "arabic": "Arabic (العربية)", "bengali": "Bengali (বাংলা)", "chinese": "Chinese (中国人)", "french": "French (Français)", "russian": "Russian (Русский)", "vietnamese": "Vietnamese (Tiếng Việt)", "receiverEmail": "Receiver <PERSON><PERSON>", "paid": "Paid", "stepAmountError": "The value must be within multiples of ", "profileUpdateSuccess": "Your profile updating successfully", "webhookUrl": "Webhook Url", "feedback": "<PERSON><PERSON><PERSON>", "feedbackSentSuccessfully": "Thank you, feedback sent successfully", "yourFeedback": "Your feedback...", "feedbackDescription": "Any suggestion or enhancement please let us know. Thanks for your time 🤍", "conversionFee": "Conversion fee", "previous": "Previous", "continue": "Continue", "next": "Next", "skip": "<PERSON><PERSON>", "showWalkthrough": "Show Walkthrough", "paymentLandingTitle": "Kazawallet Payment Gateway", "paymentLandingDescription": "Kazawallet unveils a gateway to global business transactions with its integrated payment solution. Experience secure and swift financial operations, catering to businesses and websites, and accepting over 50 global payment systems.", "learnMore": "Learn More", "whyWalletMerchantTitle": "Why Kazawallet Payment Gateway?", "whyWalletMerchantDescription": "Because Kazawallet's advanced Payment Gateway, Powers businesses and individuals alike, our secure and efficient gateway revolutionizes the way you conduct transactions.", "whyWalletMerchantListTitle": "Key Features:", "whyWalletMerchantListItem1": "Supporting transactions from over 50 payment systems worldwide.", "whyWalletMerchantListItem2": "Safeguarded by cutting-edge security measures, ensuring a secure financial experience.", "whyWalletMerchantListItem3": "Swift processing capabilities, streamlining transactions for ultimate efficiency.", "becomeMerchant": "Get <PERSON> Account", "integrationMerchantTitle": "Easy Integration with Kazawallet WordPress Plugin", "integrationMerchantDescription": "Transform your WordPress site into a powerhouse of secure transactions with the Kazawallet Payment Gateway Plugin. Effortlessly integrate our user-friendly plugin to enhance your website's payment capabilities.", "howToInstall": "How to Install?", "featuresMerchantFeat1Title": "1. Invoice", "featuresMerchantFeat1Description": "Generate and manage invoices effortlessly", "featuresMerchantFeat2Title": "2. Over 50 Payment Systems", "featuresMerchantFeat2Description": "Accept payments from a diverse range of over 50 payment systems", "featuresMerchantFeat3Title": "3. <PERSON>, <PERSON>", "featuresMerchantFeat3Description": "Enjoy the benefits of a free installation with no hidden fees", "featuresMerchantFeat4Title": "4. Wide Range of Currencies", "featuresMerchantFeat4Description": "Kazawallet supporting transactions in 5 cryptocurrencies and 10 global currencies.", "apiIntegrationMerchantTitle": "Kazawallet API Integration Guide", "apiIntegrationMerchantDescription": "Discover the power of Kazawallet Payment Gateway with our comprehensive API Documentation. Designed for developers and businesses, our documentation provides clear, step-by-step instructions for smooth integration.", "exploreApiDocumentation": "Explore API Documentation", "apiIntegrationMerchantFeat1Title": "1. Create Payment Link", "apiIntegrationMerchantFeat1Description": "Generate a payment link for a specified amount and currency", "apiIntegrationMerchantFeat2Title": "2. Webhook Notification", "apiIntegrationMerchantFeat2Description": "Set up a webhook URL to receive instant notifications when users complete payments.", "apiIntegrationMerchantFeat3Title": "3. Security Verification", "apiIntegrationMerchantFeat3Description": "Employ advanced security measures to verify the authenticity and integrity of transactions.", "apiIntegrationMerchantFeat4Title": "4. Cross-Platform Integration", "apiIntegrationMerchantFeat4Description": "Receive payment notifications via your preferred platform, whether it's Telegram or any other, for prompt and convenient updates.", "formMerchantTitle": "Ready to Level Up Your Business?", "formMerchantDescription": "Become a Kazawallet Merchant user Now.", "companyName": "Company Name", "businessEmail": "Business Email", "message": "Message", "faqMerchantTitle": "Frequently Asked Questions (FAQs)", "faq1": "What is the process to become a merchant user on Kazawallet?", "answer1": "Becoming a merchant user on Kazawallet is a straightforward process. Visit the Payment Gateway landing page, fill out the form with your information, and expect your application to be reviewed within 48 WH (work hours).", "faq2": "How can I generate a payment link using the Kazawallet Payment Gateway?", "answer2": "To generate a payment link, make a POST request to the provided API endpoint, including essential details such as amount, currency, email, and a unique reference.", "faq3": "Which payment methods does the Kazawallet Payment Gateway support?", "answer3": "The Kazawallet Payment Gateway supports a diverse range of over 50 payment systems, ensuring a broad spectrum of options for seamless and flexible transactions.", "faq4": "How can I receive timely notifications for completed payments?", "answer4": "Establish a webhook URL in your profile to receive instantaneous notifications when users successfully complete payments. The webhook includes crucial details like order ID, amount, and status.", "faq5": "Are there any deductions upon transfer?", "answer5": "No, you will receive the amount as the customer paid it to you, without any deductions from us. Enjoy seamless transactions with no additional fees.", "faq6": "Is it possible to integrate Kazawallet Payment Gateway with a Telegram bot?", "answer6": "Yes, streamline communication by integrating a Telegram bot to receive payment notifications, facilitating quick and convenient updates on completed transactions.", "requestSentSuccessfully": "Your request sent successfully", "merchants": "Merchants", "downloadPlugin": "Download Plugin", "lastSearch": "Last Search", "actions": "Actions", "profileNote": "Note: you can’t change the First Name, Last Name and the country once they are set.", "clearSearch": "Clear Search History", "copyYourAccountId": "Copy Your Account ID", "spotlightPlaceholder": "Search actions, wallets, history ……", "profile": "Profile", "noThingToShow": "No ٌResults To Show", "paymentDetails": "Payment Details", "network": "Network", "address": "Address", "payIn": "Pay In", "alert": "<PERSON><PERSON>!", "alertText": "To avoid any delay or cancellation, you must pay attention to the following: <br/> Send the exact amount written. <br/> Send the amount in one single payment. <br/> Within the specified time for the payment. <br/> <br/> Do not send to a different network or currency, as this will result in their loss and the permanent loss of the amount.", "showPaymentAddress": "Show Payment Address", "notification": "Notifications", "markAsRead": "Mark all as read", "notificationExample": "This is a notification example", "notificationDesc": "This is a example of notification desc example, fill with dummy data to test purpose only.", "originAmount": "Origin Amount", "checkFileTransfers": "Upload and Check", "ready": "Ready", "toBeReceivedAmount": "To Be Received Amount", "toBePaid": "To Be <PERSON>", "toBeReceivedError": "The received amount is less than or equal the fee you should increase the amount", "minMaxError": "The ' To Be Paid ' value must be between the maximum and minimum and not exceed the balance", "noNotification": "There is no notification to show", "clickToSeeDetails": "Click to see details", "back": "back", "loadMore": "load more", "noFee": "No Fees", "paymentLinkInvalid": "The payment link is invalid.", "helpTitle": "Welcome to Our Help Center", "helpDescription": "Get the assistance you need with our comprehensive support resources. Navigate our Help Center for self-service guidance or reach out to us on Telegram or live chat widget for personalized support.", "badges": "Tiers", "installApp": "Install App", "selectCategory": "Select Category", "attention": "Attention", "exportToCsv": "Export to CSV", "filters": "Filters", "clearAll": "Clear All", "pickDateAndTime": "Pick Date and Time", "last24Hours": "Last 24 Hours", "lastWeek": "Last Week", "lastMonth": "Last Month", "custom": "Custom", "installPwa": "Install PWA", "blog": "Blog", "addRemoveCurrencies": "Add/Remove Currencies", "chooseCurrency": "<PERSON><PERSON>", "choosePaymentMethod": "Choose Payment Method", "editWallets": "Edit wallets", "startSellingYourProduct": "Start selling your product", "home": "Home", "merchantsList": "Merchants List", "visitStore": "Visit Store", "viewMore": "View More", "accountBlocked": "Your account is locked,", "accountBlockedChangeIPErrorFirstSection": "please check your email", "accountBlockedChangeIPErrorSecondSection": "and enter the verification code to proceed", "accountBlockedError": "Your account has been restricted due to some unusual activities, \n please contact customer support for more info", "unblockAccount": "Unblock Account", "resendCode": "Resend Code", "sendVerificationCode": "Send Verification Code", "never": "Never send verification code", "ipChange": "Send when IP-address change", "subnetChange": "Send when subnet change", "browserChange": "Send when browser change", "always": "Always send code", "security": "Security", "merchant": "Merchant", "securitySettingsUpdateSuccess": "Security Settings Updated Successfully", "merchantSettingsUpdateSuccess": "Merchant Settings Updated Successfully", "enableGoogleAuthenticator": "Enable google authenticator code verification", "memo": "Memo", "operation": "Operation", "limits": "Tier limits", "giftCards": "Gift Cards", "redeemGiftCard": "Redeem Gift Card", "redeemYourGiftCard": "Redeem Your Gift Card", "code": "Code", "enterGiftCardCode": "Enter gift card code", "redeem": "Redeem", "createGiftCard": "Create Gift Card", "createNewGiftCard": "Create New Gift Card", "giftsList": "Gifts Cards History", "newGiftCardCode": "New Gift Card Code", "used": "Used", "notUsed": "Not Used", "createdAt": "Created At", "expirationDate": "Expiration Date", "usedAt": "Used At", "expiryTo": "Expiry To", "giftCardCodeNote": "This code will only be displayed once and cannot be retrieved again. If you don't save it now, you will lose it, and there is no way to recover it later. Make sure to store it securely before closing this window.", "expired": "Expired", "giftCardNoteLabel": "Important: Please copy your gift card code now!", "goToLimits": "Go To Tire Limits ", "operationsCount": "Operations Count", "minMaxErrorGift": "The value must be between the maximum and minimum and not exceed the balance", "giftCodeNote": "The redemption code is a 16 character sequence combining digits and letters.\n Example: ", "redeemNote": "<PERSON><PERSON><PERSON><PERSON> is not responsible for, and assumes no liability to you for, any unlawful conduct or fraud by any third party associated with any Gift Card.", "ok": "Ok", "operationFailed": "Operation Failed", "redeemThisGift": "Are you sure you want to redeem this gift card?", "orders": "Orders", "limitsPerDay": "Tier Limits Per Day", "uid": "<PERSON><PERSON>", "giftCardRedeemSuccessfully": "The gift card has been successfully redeemed, and {{value}} has been added to your wallet", "goToWallet": "Go To Wallet", "redeemNewGiftCard": "Redeem New Gift Card", "byGiftCard": "Made using a gift card", "notVerified": "Not Verified", "verified": "Verified", "id1": "ID 1", "id2": "ID 2", "selfie": "A selfie showing your ID and a handwritten note with 'Ka<PERSON><PERSON><PERSON>' and the current date", "kycUpdateSuccess": "KYC Update Success", "telegramOrWhatsapp": "Telegram or Whatsapp", "idFront": "ID Front", "idBack": "ID Back", "idFrontNote": "Clear image without reflection, showing the four edges. Edited images are not accepted", "idBackNote": "Clear image without reflection, showing the four edges. Edited images are not accepted", "idSelfieNote": "Clear image without reflection. Edited images are not accepted. Write the date and name of the Kazawallet clearly and large", "kycNote": "Please note that to complete the verification process, Kasroad LLC has the right Request additional documents in the future if the files attached above are insufficient", "kycWatchVideo": "Watch Video", "kycStepByStepGuide": "Step-by-Step Guide", "agreeOn": "Agree on", "termsAndConditions": "Terms and Conditions", "disabledNote": "You are not eligible to do KYC", "approvedNote": "Your KYC has been approved", "agents": "Agents", "shopWithKazaWallet": "Shop With KazaWallet", "getBestDealBuyingYourFavoriteThingsWithKazaWallet": "Get best deal buying your favorite things with KazaWallet", "becomeAMerchant": "Become a Merchant", "explainToUsHowYouWork": "Explain to us how you work", "websiteOrBot": "Website or Bot Link", "preferredSocialMedia": "Preferred Social Media", "phone": "Phone Number", "shouldBeRegisteredInKazawallet": "(must be registered with Kazawallet)", "confirmAgentAccountNumber": "Make sure the agent's account number is correct", "agentDeposit": "Agent <PERSON><PERSON><PERSON>", "showInfo": "Show Info", "enterOperationNo": "Enter Operation No", "withdrawRequestDetails": "Withdrawal Request Details", "yourCommission": "Your Commission", "attachDocuments": "Please attach the required documents", "submit": "Submit", "toBeDeposited": "To Be Deposited", "marketName": "Business Name", "jobType": "Business Model", "addressDetails": "Detailed Address", "googleMapLocation": "Google Maps Link for Address", "currenciesSupport": "Currencies You Want to Offer Agency for (Not Required)", "liquidity": "Available Liquidity for Operations", "whyBecomeAgent": "Why Become a Kazawallet Agent?", "whyBecomeAgentDesc": "Becoming a Kazawallet agent means offering your customers the ability to deposit and withdraw fiat currencies like USD, SYP, AED, EGP, …etc. quickly and securely. Kazawallet agents help meet the growing demand for financial services across Syria.", "joinAgentToday": "Join as an Agent Today", "howDosAgentProgramWork": "How Does the Kazawallet Agent Program Work?", "howDosAgentProgramWorkDesc": "Getting started as a Kazawallet agent is simple! Follow a few quick steps, and you’ll be ready to quickly provide secure and convenient financial services to your customers.", "watchHowItWorks": "Watch How It Works", "readTheFullGuide": "Read the Full Guide", "userFriendlyPlatform": "User-Friendly Platform", "userFriendlyPlatformDesc": "Manage transactions easily with our intuitive and streamlined system.", "highCommissions": "High Commissions", "highCommissionsDesc": "Earn competitive commissions with every completed transaction.", "instantUpdates": "Instant Updates", "instantUpdatesDesc": "Get real-time updates and notifications for each transaction.", "dedicatedSupport": "Dedicated Support", "dedicatedSupportDesc": "Our support team is here to assist you whenever you need help.", "readyToBecomeAgent": "Ready to Become a Kazawallet Agent?", "readyToBecomeAgentDesc": "Sign Up Now and Start Offering Easy, Secure Payments!", "agentQ1": "What are the basic requirements to become a KazaWallet agent?", "agentA11": "Availability of a fixed shop or office.", "agentA12": "Providing the required personal documents.", "agentA13": "Signing the agency agreement.", "agentQ2": "How much capital is required to become an agent?", "agentA21": "A minimum balance of $500 or its equivalent, divided between cash and the wallet.", "agentQ3": "Is there any guarantee for the agent’s rights in case of a malfunction or wallet hacking?", "agentA31": "The wallet is secured with the highest global protection standards to ensure the rights of all users.", "agentA32": "Additionally, a legal agreement is signed by a lawyer to guarantee the rights of both the wallet and the agent.", "agentQ4": "Is the wallet secure?", "agentA41": "Absolutely! The wallet handles thousands of financial transactions daily with a high level of security and protection.", "agentQ5": "Why is there no deposit commission?", "agentA51": "To provide cash liquidity for agents and encourage customers to deposit into the wallet through agents.", "agentQ6": "What are the working hours?", "agentA61": "Agents can work at their convenience, provided they are available during official working hours, from 11 AM to 4 PM.", "agentPageSeoTitle": "Become a Kazawallet Agent – Earn High Commissions", "agentPageSeoDesc": "Join the Kazawallet agent program to provide secure fiat currencies like USD, SYP, AED, EGP, etc. financial services. Earn high commissions, access real-time updates, and receive 24/7 support.", "kazawalletAgentProgram": "Kazawallet Agent Program", "totalFee": "Total Fee", "minimumFeePerOrder": "Minimum Fee Per Order", "agentCommission": "Agent Commission", "minimumAgentCommissionPerOrder": "Minimum Agent Commission Per Order", "kazawalletCommission": "Kazawallet Commission", "commissionStructureForKazawalletAgents": "Commission Structure for Kazawallet Agents", "agentProgramLandingTitle": "Kazawallet Agents Program", "agentProgramLandingDescription": "Join the Kazawallet agent network and enjoy tailored technical support and exclusive updates to continuously boost your profits. Earn competitive commissions and provide secure, advanced financial solutions to your clients through our platform!", "joinOurAgentsNow": "Join Our Agents Now", "buy": "Buy", "sell": "<PERSON>ll", "noLimit": "No Limit Found", "noBadge": "No tiers has been added yet", "noBadgeLimitPaymentMethods": "No payment gateway limits", "noBadgeLimitOperation": "No operation limits", "operations": "Operations", "limitsPerDayDescription": "Your account's daily limits apply only to the payment methods and currencies listed below", "depositAddressWarning": "Use this deposit address for this order only. Do not save or reuse it, as it may change.", "preferredCommunicationMethod": "Preferred communication method", "cryptoPriceToUSD": "Crypto currencies exchange rates to the US dollar", "fiatPriceToUSD": "Fiat currencies exchange rates to the US dollar", "KYC": "KYC", "kazawalletDisclaimer": "Kazawallet is a digital financial platform (not a bank), operating under Kasroad FZ-LLC (License No.: 2527445.01). All financial services available through the platform are provided by duly licensed partners and service providers.", "receiveQrCode": "Receive", "backToScanner": "Back to scanner", "qrScanner": "QR Scanner", "downloadQrAsImage": "Download as image", "album": "Album", "aimToScanQrCode": "Aim to scan a QR code", "orSelectFromAlbum": "Or select one from your photo album", "share": "Share", "copyAddress": "Copy Address", "yourWalletAddress": "KazaWallet account number", "scanQRCode": "Scan QR Code", "enterAccountIdOrEmail": "Enter account ID or email", "processingImage": "Processing image...", "qrScanSuccessful": "QR scan successful", "myCard": "My Card", "getNewCard": "Get New Card", "cardBalance": "Card balance", "topUp": "Top Up", "freezeCard": "Freeze card", "merchantSupported": "Merchant supported", "details": "Details", "addToGooglePayAndApplePay": "Add to Google Pay & Apple Pay", "activities": "Activities", "transactions": "Transactions", "cardMaintenance": "Card Maintenance", "transaction": "Transaction", "noTransactionsFound": "No transactions found", "securityCheck": "Security check", "pciDssCompliance": "To comply with PCI DSS, full card details are shown only to the cardholder after additional verification. You'll have a limited time to view them.", "redeemCard": "Redeem a card", "flexiCardTitle": "FLEXI Card", "flexiCardDescription": "Shop and spend your cryptos assets directly with a Mastercard® or Visa® card", "thirdPartyPayment": "Third-party payment", "ai": "AI", "onlinePayments": "Online Payments", "activationFee": "Activation fee", "initialTopUp": "Initial top-up", "topUpFee": "Top-up fee", "monthlyFee": "Monthly fee", "generalTxnFee": "General txn fee", "foreignTxnFee": "Foreign txn fee", "apply": "Apply", "comingSoon": "Coming Soon", "detailsCredentials": "Details/Credentials", "cardNumber": "Card Number", "expiryDate": "Expiry Date", "cardHolderName": "Card Holder Name", "billingAddress": "Billing Address", "viewCredentials": "View Credentials", "hideCredentials": "Hide Credentials", "enterPin": "Enter PIN", "pinDescription": "Please enter your 6-digit PIN to continue", "verify": "Verify", "transactionDetails": "Transaction Details", "makeNote": "Make Note", "addNote": "Add Note", "saveNote": "Save Note", "privateNote": "Private Note", "enterNote": "Enter your note here...", "noteAdded": "Note added successfully", "netAmountCredited": "Net amount credited", "txnTime": "Txn time", "descriptor": "Descriptor", "txnRef": "Txn ref", "edit": "Edit", "makeANote": "Make a note", "makePrivateNote": "Make a private note", "privateNoteDescription": "You can add a private note to annotate this transaction for your personal records. Only you will see this note.", "enterPrivateNote": "Enter private note (optional)", "securityVerificationRequired": "Security Verification Required", "pciDssStandards": "To comply with PCI DSS standards, full card details are shown only to the cardholder after additional verification. You'll have a limited time to view them.", "verificationSuccessful": "Verification Successful!", "verificationFailed": "Verification Failed", "incorrectPin": "Incorrect PIN. Please try again.", "verifying": "Verifying...", "clickToCopyCardNumber": "Click to copy card number", "clickToCopyExpiryDate": "Click to copy expiry date", "clickToCopyCvv": "Click to copy CVV", "exp": "Exp", "cvv": "CVV", "hidingIn": "Hiding in", "seconds": "s", "clickToViewCredentials": "Click to view your card credentials", "keepCardDetailsPrivate": "Keep your card details private. Anyone with this information can make purchases on your card.", "windowAutoClose": "This window will automatically close in", "cardCredentials": "Card credentials", "tapToCopy": "Tap any row to copy the content", "nameOnCard": "Name On Card", "expiryDateMmYy": "Expiry Date (MM/YY)", "cvvCvc": "CVV/CVC", "furtherBillingInfo": "Further billing information", "nativeCurrency": "Native Currency", "phoneEmailVerification": "Your phone and email, if listed here, may be needed to verify transactions.", "billingAddressInfo": "Billing addresses are sometimes used by merchants to confirm your identity during transactions.", "cardNotFound": "Card not found", "cardNotFoundDescription": "The requested card type could not be found.", "initialTopUpLabel": "Initial top-up", "minimumInitialTopUp": "Minimum initial top-up is 100 USD.", "activationFeeLabel": "Activation fee", "totalPayableNow": "Total payable now", "payWith": "Pay with", "accountBalance": "Account balance", "activateCardInOneMinute": "Activate your card in 1 minute", "card": "card", "freezCard": "Freez Card"}