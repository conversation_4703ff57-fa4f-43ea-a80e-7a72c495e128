/* eslint-disable max-len */
import {
  Box, Button, Group, Text, Title, Stack, Anchor,
} from '@mantine/core';
import {
  IconArrowLeft, IconBrandReddit,
  IconBrandTwitter, IconBrandAirbnb,
  IconBrandOpera, IconBrandSpotify,
  IconBrandDiscord, IconBrandNetflix,
  IconBrandTinder, IconBrandUber,
  IconBrandVisa, IconBrandMastercard, IconBrandYoutube, IconBrandZoom,

} from '@tabler/icons-react';
import { Carousel } from '@mantine/carousel';
import Autoplay from 'embla-carousel-autoplay';
import { useRef } from 'react';
import { useRouter } from 'next/router';
import useTranslation from 'next-translate/useTranslation';
import { FlexiCardComparison } from '@/components/flexi-card/flexi-card-comparison';
import { FlexiCardOffers } from '@/components/flexi-card/flexi-card-offers';
import { Layout } from '@/components/layout/layout';

export default function FlexiCardApply() {
  const { t } = useTranslation();
  const router = useRouter();
  const autoplay = useRef(
    Autoplay({
      delay: 1000,
      stopOnInteraction: false,
      stopOnMouseEnter: false,
    }),
  ).current;

  return (
    <Layout>
      <Stack spacing="xl">
        <Group position="apart">
          <Button
            variant="subtle"
            leftIcon={<IconArrowLeft size={16} />}
            onClick={() => router.push('/flexi-card')}
          >
            {t('common:back')}
          </Button>
          <Anchor href="#" size="sm">
            {t('common:redeemCard')}
          </Anchor>
        </Group>

        <Box>
          <Title order={1}>{t('common:flexiCardTitle')}</Title>
          <Text>{t('common:flexiCardDescription')}</Text>
        </Box>
        <FlexiCardOffers />
        <Carousel
          slideSize="10%"
          slideGap="xs"
          loop
          align="start"
          slidesToScroll={1}
          withControls={false}
          plugins={[autoplay]}
          styles={{
            viewport: { overflow: 'hidden' },
            container: { display: 'flex', alignItems: 'center' },
          }}
        >
          <Carousel.Slide><IconBrandReddit size={48} /></Carousel.Slide>
          <Carousel.Slide><IconBrandTwitter size={48} /></Carousel.Slide>
          <Carousel.Slide><IconBrandAirbnb size={48} /></Carousel.Slide>
          <Carousel.Slide><IconBrandOpera size={48} /></Carousel.Slide>
          <Carousel.Slide><IconBrandSpotify size={48} /></Carousel.Slide>
          <Carousel.Slide><IconBrandDiscord size={48} /></Carousel.Slide>
          <Carousel.Slide><IconBrandNetflix size={48} /></Carousel.Slide>
          <Carousel.Slide><IconBrandTinder size={48} /></Carousel.Slide>
          <Carousel.Slide><IconBrandUber size={48} /></Carousel.Slide>
          <Carousel.Slide><IconBrandVisa size={48} /></Carousel.Slide>
          <Carousel.Slide><IconBrandMastercard size={48} /></Carousel.Slide>
          <Carousel.Slide><IconBrandYoutube size={48} /></Carousel.Slide>
          <Carousel.Slide><IconBrandZoom size={48} /></Carousel.Slide>
        </Carousel>
        <FlexiCardComparison />
      </Stack>
    </Layout>
  );
}
